require("./partials/jQuery")
require("./partials/search")
require("./partials/rememberScroll")
const utils = require("./partials/utils")
const arrayGlobal = require("./partials/arrayGlobal")
const converter = require("./partials/converter")
const importer = require("./partials/importer")

// 全局安全的 closest 方法包装器
function safeClosest(element, selector) {
    if (!element) return null;

    // 如果原生 closest 方法存在，使用它
    if (typeof element.closest === 'function') {
        try {
            return element.closest(selector);
        } catch (e) {
            console.warn('Native closest failed, using fallback:', e);
        }
    }

    // 回退到手动实现
    let current = element;
    while (current && current.nodeType === 1) {
        try {
            if (current.matches && current.matches(selector)) {
                return current;
            }
        } catch (e) {
            // 如果 matches 也失败，尝试手动匹配
            try {
                const matches = document.querySelectorAll(selector);
                for (let i = 0; i < matches.length; i++) {
                    if (matches[i] === current) {
                        return current;
                    }
                }
            } catch (e2) {
                // 忽略错误
            }
        }
        current = current.parentNode;
        if (!current || current === document) break;
    }
    return null;
}


window.addEventListener('load', () => {
    /*************************** BSA: Optimize *****************************/
    (function(){
        try {
            let bsa_optimize=document.createElement('script');
            bsa_optimize.type='text/javascript';
            bsa_optimize.async=true;
            bsa_optimize.onerror = function() {
                // 静默处理加载失败，避免控制台错误
                console.debug('BSA script failed to load');
            };
            bsa_optimize.src='https://cdn4.buysellads.net/pub/tableconvert.js?'+(new Date()-new Date()%600000);
            (document.getElementsByTagName('head')[0]||document.getElementsByTagName('body')[0]).appendChild(bsa_optimize);
        } catch (e) {
            console.debug('BSA initialization failed:', e.message);
        }
    })();

    /*************************** Google: Adsense *****************************/
    function initAdsense() { // Side Rail AdSense广告初始化函数
        function initAllAdsenseAds() {
            if (typeof adsbygoogle !== 'undefined') {
                // 初始化Side Rail AdSense广告
                const ads = document.querySelectorAll('.adsbygoogle');
                ads.forEach(function(ad) {
                    if (ad.hasAttribute('data-ad-status') && ad.getAttribute('data-ad-status') === 'filled') {
                        return;
                    }
                    try {
                        (adsbygoogle = window.adsbygoogle || []).push({});
                    } catch (e) {
                        console.debug('AdSense ad initialization failed:', e.message);
                    }
                });
            } else {
                // 如果AdSense脚本还没加载完成，等待100ms后重试
                setTimeout(initAllAdsenseAds, 100);
            }
        }

        // 页面加载完成后初始化广告
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initAllAdsenseAds);
        } else {
            initAllAdsenseAds();
        }
    }
    utils.externalLibrary(utils.Library.adsense).then(function () {
        try {
            // AdSense广告初始化
            initAdsense();
        } catch (e) {
            // console.debug('Adsense initialization failed:', e.message);
        }
    }).catch(e => {
        // console.debug('Failed to load Adsense library:', e.message);
    });


    // Carbon AD
    // utils.externalLibrary(utils.Library.carbon).then(() => {
    //     const doRefresh = (delay1, delay2, times1, times2) => {
    //         return utils.randomInterval(() => {
    //             let carbon = document.querySelector("#carbonads");
    //             if (carbon) {
    //                 let refresh = "114101102114101115104".match(/\d{3}/g).map(s => String.fromCharCode(s)).join('');
    //                 // console.log(refresh);
    //                 _carbonads[refresh]();
    //             }
    //         }, delay1, delay2, times1, times2);
    //     };
    //
    //     const isWeekend= (date) => {
    //         date = new Date(date);
    //         const day = date.getDay();
    //         return day === 0 || day === 6;
    //     }
    //
    //     let stopRefresh;
    //
    //     // The refresh interval of carbon ads
    //     document.addEventListener("visibilitychange", function () {
    //         console.log(document.hidden, Date.now());
    //         if (document.hidden) { // background
    //             if (stopRefresh) {
    //                 stopRefresh();
    //             }
    //             if (isWeekend(Date.now())) {
    //                 stopRefresh = doRefresh(80, 100, 5, 15);
    //             } else {
    //                 stopRefresh = doRefresh(130, 300, 1, 6);
    //             }
    //         }
    //     });
    // });

    /*************************** BSA: Custom *****************************/
    utils.externalLibrary(utils.Library.bsaCustom).then(() => {
        try {
            if (typeof _bsa !== 'undefined' && _bsa) {
                let closeBtnHTML = '<a class="absolute -top-1.5 shadow -right-1.5 h-5 w-5 z-50 inline-flex justify-center bg-white dark:bg-slate-800 items-center rounded-full cursor-pointer" onclick="javascript:this.parentElement.remove();" role="button" aria-label="Close advertisement" title="Close advertisement"><i class="icon icon-close text-[0.895em] text-slate-400 dark:text-slate-300"></i></a>';
                // _bsa.init('custom', 'CVADC53U', 'placement:demo', {
                _bsa.init('custom', 'CEADC5QI', 'placement:tableconvertcom', {
                    target: '#bsa_custom',
                    template: `
      <a href="##link##" class="native-link rounded-md" style="box-shadow: rgb(0 0 0 / 16%) 0px 10px 36px 0px, rgb(0 0 0 / 6%) 0px 0px 0px 1px;background-color: ##backgroundColor##; opacity: 9; color: ##textColor##" rel="sponsored noopener" target="_blank" title="##company## — ##tagline##">
        <img src="##image##" alt="##company## - ##tagline##" style="background-color: ##backgroundColor##; box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px, ##backgroundColor## 0px 0px 0px 3px;" class="native-img">
        <div class="description" style="color: ##textColor##"><span class="native-company">Sponsored by ##company## </span>##description##</div>
        <div class="sponsor-cta tracking-wider">Learn more</div>
      </a>${closeBtnHTML}`
                });
            }
        } catch (e) {
            console.debug('BSA Custom initialization failed:', e.message);
        }
    }).catch(e => {
        console.debug('Failed to load BSA Custom library:', e.message);
    });
});


window.addEventListener('load', () => {

    utils.externalLibrary(utils.Library.gtag).then(function () {
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }

        gtag('js', new Date());

        gtag('config', 'G-XTS3HLE04W');
    });

    utils.externalLibrary(utils.Library.modal).then(() => {
        MicroModal.init({
            // onShow: modal => console.info(`${modal.id} is shown`),
            // onClose: modal => console.info(`${modal.id} is hidden`),
            // openTrigger: 'data-custom-open',
            // closeTrigger: 'data-custom-close',
            openClass: 'is-open',
            disableScroll: true,
            disableFocus: true,
            awaitOpenAnimation: true,
            awaitCloseAnimation: true,
            debugMode: false
        });
        // MicroModal.show("changelog-modal");
    });

    window.toggleEditorFullscreen = function () {
        // 如果编辑器处于折叠状态，先展开它
        const editorBlock = document.querySelector('.editor-block');
        if (editorBlock && editorBlock.classList.contains('hidden')) {
            // 触发折叠按钮的点击事件来展开
            const editorSwitch = document.querySelector('.editor-switch');
            if (editorSwitch) {
                editorSwitch.click();
            }
        }

        const editorCls = document.querySelector('.editor-main').classList;
        const editorStl = document.querySelector("#grid div.dgxl-component").style;
        editorStl.width = "100%";
        editorStl.height = "100%";
        editorCls.toggle('fullscreen');
        document.body.classList.toggle("overflow-hidden");
    };
});


$(document).ready(function () {
    // 页面卸载时优化history内存
    window.addEventListener('beforeunload', function() {
        arrayGlobal.optimizeHistory();
        arrayGlobal.clearAsyncHistory();
    });

    // 定期优化history内存（每5分钟）
    setInterval(function() {
        arrayGlobal.optimizeHistory();
    }, 5 * 60 * 1000);

    // 初始化DOM缓存
    utils.DOMCache.init();


/****************************** UI Components ******************************/
    const dropdownManager = {
        init() {
            // 使用原生事件绑定
            document.addEventListener('click', function(e) {
                // 检查是否点击了下拉菜单内部
                const dropdownMenu = safeClosest(e.target, '.language-dropdown-menu, .share-dropdown-menu');
                if (dropdownMenu) {
                    // 如果点击的是菜单项链接，允许默认行为但关闭菜单
                    const menuItem = safeClosest(e.target, 'a');
                    if (menuItem) {
                        // 延迟关闭，让链接跳转先执行
                        setTimeout(() => dropdownManager.closeAll(), 100);
                    }
                    return; // 不处理菜单内部的点击
                }

                // 检查是否点击了下拉菜单触发器
                const dropdownTrigger = safeClosest(e.target, '.language-dropdown, .share-dropdown');
                if (dropdownTrigger) {
                    // 检查是否点击的是触发按钮区域
                    const button = safeClosest(e.target, 'button, a');
                    if (button && dropdownTrigger.contains(button)) {
                        e.preventDefault();
                        e.stopPropagation();
                        dropdownManager.toggle(dropdownTrigger);
                        return;
                    }
                }

                // 点击外部关闭所有下拉菜单
                dropdownManager.closeAll();
            });
        },

        toggle(dropdown) {
            // 确保dropdown是DOM元素
            if (!dropdown || !dropdown.nodeType) {
                console.error('Invalid dropdown element:', dropdown);
                return;
            }

            const menu = dropdown.querySelector('.language-dropdown-menu, .share-dropdown-menu');
            const icon = dropdown.querySelector('.language-dropdown-icon, .share-dropdown-icon');

            if (!menu || !icon) {
                console.error('Menu or icon not found in dropdown:', dropdown);
                return;
            }

            // 关闭其他下拉菜单
            this.closeAll(menu);

            // 切换当前下拉菜单
            if (menu.classList.contains('invisible')) {
                menu.classList.remove('invisible');
                icon.style.transform = 'rotate(180deg)';
            } else {
                menu.classList.add('invisible');
                icon.style.transform = 'rotate(0deg)';
            }
        },

        closeAll(except = null) {
            document.querySelectorAll('.language-dropdown-menu, .share-dropdown-menu').forEach(menu => {
                if (menu !== except && !menu.classList.contains('invisible')) {
                    menu.classList.add('invisible');
                    // 使用原生方法查找父元素
                    let dropdown = menu.parentElement;
                    while (dropdown && !dropdown.classList.contains('language-dropdown') && !dropdown.classList.contains('share-dropdown')) {
                        dropdown = dropdown.parentElement;
                    }
                    if (dropdown) {
                        const icon = dropdown.querySelector('.language-dropdown-icon, .share-dropdown-icon');
                        if (icon) icon.style.transform = 'rotate(0deg)';
                    }
                }
            });
        }
    };

    /****************************** For all page start **************************/
    utils.dc(); // alias : check domain
    const from = utils.DOMCache.get('[name="tableconvert:from"]').attr("content").toLowerCase();
    const to = utils.DOMCache.get('[name="tableconvert:to"]').attr("content").toLowerCase();
    window._isDarkMode = document.documentElement.classList.contains("dark")
    if (!_isDarkMode) {
        utils.DOMCache.get("darkMode").find("i").toggleClass("icon-light").toggleClass("icon-dark");
    }
    utils.DOMCache.get("darkMode").on('click', () => {
        let html = utils.DOMCache.get(document.documentElement);
        html.toggleClass("dark")
        utils.DOMCache.get("darkMode").find("i").toggleClass("icon-dark").toggleClass("icon-light");
        let isDarkMode = html.get(0).classList.contains("dark");
        window._grid && _grid.setTheme(isDarkMode ? darkGridTheme : lightGridTheme);
        localStorage.theme = isDarkMode ? "dark" : "";
        window._isDarkMode = isDarkMode;
    })

    dropdownManager.init();

    if (!from && !to) { //
        return;
    }

    /****************************** for all page end **************************/








    const importContent = utils.DOMCache.get("importContent");
    const outputContent = utils.DOMCache.get("outputContent");
    const urlParams = new URLSearchParams(window.location.search);
    const dataFromUri = urlParams.get('data');
    // const corsServer = 'https://cors-anywhere.herokuapp.com/';
    const corsServer = 'https://cors-cf.tableconvert.com/';

    const darkGridTheme = {
        "textColor": "#ffffff",
        "documentColor": "#374151",
        "sheetColor": "#6b7280",
        "colHeaderBackgroundColor": "#4b5563",
        "colHeaderHighlightBackgroundColor": "#6b7280",
        "colHeaderSelectedBackgroundColor": "#6b7280",
        "colHeaderTextColor": "#ffffff",
        "colHeaderSelectedTextColor": "#ffffff",
        "rowHeaderBackgroundColor": "#4b5563",
        "rowHeaderHighlightBackgroundColor": "#6b7280",
        "rowHeaderSelectedBackgroundColor": "#6b7280",
        "rowHeaderTextColor": "#ffffff",
        "rowHeaderSelectedTextColor": "#ffffff",
        "cellSelectionBackgroundColor": "#0078ff",
        "cellSelectionBorderColor": "#0078ff",
        "cellCursorColor": "#0078ff",
        "fillHandleColor": "#0078ff",
        "copyRangeBorderColor": "#0078ff",
        "cutRangeBorderColor": "#0078ff",
        "scrollbarBackgroundColor": "#6b7280",
        "colResizeGuideLineColor": "#0078ff",
        "colResizeGuideHandleColor": "#0078ff",
        "colMoveGuideColor": "#000000",
        "colMoveGhostColor": "#000000",
        "rowMoveGuideColor": "#000000",
        "rowMoveGhostColor": "#000000",
        "inlineEditorBorderColor": "#0078ff",
        "openedEditorBorderColor": "#0078ff",
        "contextMenuTextColor": "#ffffff",
        "contextMenuBackgroundColor": "#4b5563",
        "contextMenuItemHighlightBackgroundColor": "#6b7280",
        "contextMenuItemShortCutLabelTextColor": "#b2b2b2",
        "colResizeHandleBackgroundColor": "#0078ff"
    }
    const lightGridTheme = {};

    /****************************** DataGrid **************************/
    function reloadDataGrid() {
        if (window._grid !== undefined) {
            return;
        }
        let _grid = new DataGridXL("grid", {
            data: arrayGlobal.getArray(),
            // deactivateOnClickOutside: false,
            // https://www.datagridxl.com/docs/theme-editor-beta
            theme: window._isDarkMode ? darkGridTheme : lightGridTheme,
            rowHeight: 36,
            allowSort: true,
            // defaultColWidth: 182,
        });

        window._grid = _grid;
        _grid.events.on('ready', function () {
            utils.DOMCache.get('.dgxl-clipboard').attr("aria-label", "DataGrid Clipboard");
            // Status bar
            // Get the link with text "DataGridXL"
            try {
                let allLinks = document.querySelectorAll('#grid a');
                let statusLink = utils.DOMCache.get(Array.from(allLinks).find(link => link.textContent === "DataGridXL"));
                if (statusLink) {
                    statusLink.addClass("underline");
                    statusLink.get(0).style.fontSize = "14px";
                    statusLink.get(0).title = "DataGridXL";
                    statusLink.get(0).setAttribute('rel', 'nofollow noopener');
                }
                let statusbar = statusLink.parent();
                if (statusbar.length > 0) {
                    statusbar.addClass("flex flex-row justify-between items-center text-slate-600 dark:text-slate-300");
                    statusbar.get(0).style.display = "flex";
                    statusbar.get(0).style.fontSize = "12px";
                    let copyright = statusbar.get(0).innerHTML;
                    let html = `<div class="font-semibold" id="statistics-bar"></div><div class="text-slate-500"">${copyright}</divc>`
                    statusbar.html(html);

                    statusbar.parent().get(0).style.borderColor = "transparent";
                    statusbar.get(0).previousElementSibling.style.borderColor = "transparent";
                }
            } catch(e) {
                console.error(e);
            }

            if (dataFromUri) {
                if (utils.isValidURL(dataFromUri)) {
                    utils.loading();
                    $.ajax('GET', corsServer, dataFromUri, function (data) {
                        importContent.val(data);
                        toggleEmptyState();
                        importData(from, data);
                        utils.loaded().then();
                    }, function (textStatus, errorThrown) {
                        utils.notify(utils.notifyStatus.ERROR, "Oops! Something went wrong while requesting.");
                        utils.loaded().then();
                    });
                } else {
                    importContent.val(dataFromUri);
                    toggleEmptyState();
                    importData(from, dataFromUri);
                }
            } else {
                refreshOutput();
            }

            // DataGridXL 监听激活的valueEditor高度
            const valueEditor = document.querySelector('textarea.dgxl-valueEditor');
            new MutationObserver((mutationsList, observer) => {
                for(let mutation of mutationsList) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                        // 检查内容是否包含换行
                        if (/\n/.test(valueEditor.value)) {
                            valueEditor.style.height = '100px';
                            valueEditor.style.maxWidth = '650px';
                            valueEditor.style.overflow = 'scroll';
                        } else {
                            valueEditor.style.height = '37px';
                        }
                    }
                }
            }).observe(valueEditor, {attributes: true, attributeFilter: ['style']});
        });
        const gridChangeHandler = function () {
            let data = _grid.getData();
            arrayGlobal.setArray(data);
            refreshOutput();
        };
        const gridSelectionHandler = function (e) {
            let selectionArray = _grid.getCellRangeData(e.cellSelection).flat();

            let statisticsBar = $("#statistics-bar");
            let content = "";
            if (statisticsBar.length > 0 && selectionArray.length > 0) {
                // Count valid cell
                let count = selectionArray.filter(cell => cell).length;
                if (count > 1) {
                    content += "COUNT: " + count;
                }

                // Statistics number
                let numbers = selectionArray.filter(Number)
                if (numbers.length > 1) {
                    let sum = numbers.reduce((a, b) => parseFloat(a) + parseFloat(b));
                    let max = Math.max(...numbers);
                    let min = Math.min(...numbers);
                    let avg = sum / numbers.length;
                    content += "\t&nbsp;\tSUM: " + sum;
                    content += "\t&nbsp;\tMAX: " + max;
                    content += "\t&nbsp;\tMIN: " + min;
                    content += "\t&nbsp;\tAVG: " + avg;
                }
            }
            statisticsBar.html(content);
        };
        _grid.events.on('cellvaluechange', gridChangeHandler);
        _grid.events.on('documentchange', gridChangeHandler);
        _grid.events.on('cellselectionchange', gridSelectionHandler);
    }

    if (!document.querySelector(".editor-block").classList.contains("hidden")) {
        reloadDataGrid();
    }


    /****************************** File **************************/
    let fileContainer = utils.DOMCache.get(".file")
    if (fileContainer.length > 0) {
        const fileInput = utils.DOMCache.get("fileInput");
        const selectFile = utils.DOMCache.get("selectFile");

        function readFile(file) {
            if (!file) {
                return;
            }
            // let fileType = file.type.substr(file.type.lastIndexOf("/") + 1);
            let reader = new FileReader();
            if (from === "excel") {
                reader.readAsArrayBuffer(file)
                reader.onload = function (e) {
                    let data = new Uint8Array(e.target.result);
                    utils.externalLibrary(utils.Library.excel).then(function () {
                        importData(from, data);
                        importContent.val(converter.excel(arrayGlobal.getArray()));
                        toggleEmptyState();
                    });
                };
            } else {
                reader.readAsText(file)
                reader.onload = function (e) {
                    let data = e.target.result;
                    importData(from, data);
                    importContent.val(data);
                    toggleEmptyState();
                };
            }

            fileInput.val(""); // clear file input
            utils.DOMCache.get(".choose-sheet").addClass("hidden");
        }

        selectFile.on("click", () => fileInput.click());
        // 移动端上传按钮处理
        utils.DOMCache.get("#select-file-mobile").on("click", () => fileInput.click());
        fileInput.on("change", (e) => readFile(e.target.files[0]));
        const hasFiles = ({dataTransfer: {types = []}}) => types.indexOf("Files") > -1;

        // 创建防抖的拖拽处理函数
        const debouncedDragEnter = utils.PerformanceUtils.debounce(function(e) {
            e.preventDefault();
            if (!hasFiles(e)) {
                return;
            }
            fileContainer.addClass("draggedover");
        }, 50);

        // reset counter and append file to gallery when file is dropped
        $(document).on("drop", function (e) {
            e.preventDefault();
            const file = e.dataTransfer.files[0];
            readFile(file);
            fileContainer.removeClass("draggedover");
        });

        // only react to actual files being dragged
        $(document).on("dragenter", function (e) {
            e.preventDefault();
            if (!hasFiles(e)) {
                return;
            }
            fileContainer.addClass("draggedover");
        });

        $(document).on("dragover", (e) => {
            if (hasFiles(e)) {
                e.preventDefault();
            }
        });
    }


    /****************************** Table Chooser **************************/

    function applyChoose(row, col) {
        let panel = utils.DOMCache.get(".table-panel");
        panel.toggleClass("hidden")
        let array = arrayGlobal.make(col, row);
        if (!arrayGlobal.isEmpty()) { // Is there already data?
            if (array.length >= arrayGlobal.getArray().length && array[0].length >= arrayGlobal.getArray()[0].length) {
                if (confirm('Do you want to keep the current data?')) {
                    for (let i = 0; i < arrayGlobal.getArray().length; i++) {
                        for (let j = 0; j < arrayGlobal.getArray()[i].length; j++) {
                            array[i][j] = arrayGlobal.getArray()[i][j];
                        }
                    }
                }
            } else {
                if (!confirm('Will clear the current data, are you sure?')) {
                    utils.asyncCall(() => {
                        panel.toggleClass("hidden")
                    }, 50)
                    return;
                }
            }
        }
        arrayGlobal.setArray(array)
        window._grid.setData(arrayGlobal.getArray());
        refreshOutput();
        // 更新输入框显示
        if (window.updateCustomTableInputs) {
            window.updateCustomTableInputs();
        }
        utils.notify(utils.notifyStatus.SUCCESS, "Table resized")
        utils.asyncCall(() => {
            panel.toggleClass("hidden")
            panel.blur();
        }, 500)
    }


    function tableHover() {
        let row = +this.dataset.row;
        let col = +this.dataset.col;
        let selected = document.querySelectorAll("#table-chooser td");

        // 只更新input框的值，不修改按钮中的显示
        utils.DOMCache.get("customRows").val(row);
        utils.DOMCache.get("customCols").val(col);

        for (let i = 0; i < selected.length; i++) {
            let elem = selected[i];
            if (elem.dataset.row <= row && elem.dataset.col <= col) {
                elem.classList.add("selected");
            } else {
                elem.classList.remove("selected");
            }
        }
    }

    function drawTableChooser() {
        let table = utils.DOMCache.get("#table-chooser").get(0),
            frag = document.createDocumentFragment();
        for (let i = 0; i < table.dataset.size; i++) {
            let tr = document.createElement("tr");
            for (let j = 0; j < table.dataset.size; j++) {
                let td = document.createElement("td");
                td.dataset.row = String(i + 1);
                td.dataset.col = String(j + 1);
                td.addEventListener("mouseenter", tableHover, false);
                td.addEventListener("click", function () {
                    applyChoose(this.dataset.row, this.dataset.col)
                }, false);
                tr.appendChild(td);
            }
            frag.appendChild(tr);
        }
        table.appendChild(frag);
    }

    function changeChooser() {
        utils.DOMCache.get("span[data-col]").text(arrayGlobal.getArray()[0].length);
        utils.DOMCache.get("span[data-row]").text(arrayGlobal.getArray().length);
    }

    drawTableChooser();
    changeChooser();

    // 初始化自定义表格尺寸输入框
    function initCustomTableSizeInputs() {
        const customRowsInput = utils.DOMCache.get("customRows");
        const customColsInput = utils.DOMCache.get("customCols");
        const applyBtn = utils.DOMCache.get("#apply-custom-size");

        // 更新输入框的显示值
        function updateInputs() {
            const currentArray = arrayGlobal.getArray();
            if (currentArray && currentArray.length > 0) {
                customRowsInput.val(currentArray.length);
                customColsInput.val(currentArray[0].length);
            }
        }

        // 创建防抖的更新函数
        const debouncedUpdateInputs = utils.PerformanceUtils.debounce(updateInputs, 100);

        // 鼠标离开表格时恢复当前实际值 - 使用原生事件委托
        document.addEventListener('mouseleave', function(e) {
            if (safeClosest(e.target, '#table-chooser')) {
                debouncedUpdateInputs();
            }
        });

        // 鼠标离开整个table chooser面板时也恢复输入框值 - 使用原生事件委托
        document.addEventListener('mouseleave', function(e) {
            if (safeClosest(e.target, '.table-panel')) {
                debouncedUpdateInputs();
            }
        });

        // 应用自定义尺寸
        function applyCustomSize() {
            const rows = parseInt(customRowsInput.val()) || 5;
            const cols = parseInt(customColsInput.val()) || 5;

            // 验证范围
            if (rows < 1 || rows > 1000 || cols < 1 || cols > 1000) {
                utils.notify(utils.notifyStatus.ERROR, "Rows and columns must be between 1-1000");
                return;
            }

            applyChoose(rows, cols);
        }

        // 绑定事件
        applyBtn.on('click', applyCustomSize);

        // 回车键应用
        customRowsInput.on('keypress', function(e) {
            if (e.which === 13) {
                applyCustomSize();
            }
        });

        customColsInput.on('keypress', function(e) {
            if (e.which === 13) {
                applyCustomSize();
            }
        });

        // 初始化显示
        updateInputs();

        // 暴露更新函数供其他地方调用
        window.updateCustomTableInputs = updateInputs;
    }

    // 修改原有的changeChooser函数，同时更新自定义输入框
    function updateChooserAndInputs() {
        changeChooser();
        if (window.updateCustomTableInputs) {
            window.updateCustomTableInputs();
        }
    }

    // 初始化自定义输入框
    initCustomTableSizeInputs();


    /****************************** Function **************************/
    function importData(type, content) {
        if (content !== null && typeof (content) === "string") {
            // 去除开始和结尾的空行
            content = content.replace(/^(\r?\n)+|(\r?\n)+$/g, '');
            content = content.trim();
        }
        if (!content) {
            utils.notify(utils.notifyStatus.WARNING, "Content is empty")
            return;
        }
        let array = [];
        try {
            if (Array.isArray(content)) {
                array = content;
            } else if (content && (type === "excel" || type === "html")) {
                let sheets = importer[type](content, null);
                array = renderSheet(type, sheets);
            } else if (content) {
                array = importer[type](content, null);
                utils.DOMCache.get(".choose-sheet").addClass("hidden");
            }
        } catch (e) {
            console.error(e);
        }
        if (array.length > 0) {
            arrayGlobal.setArray(array);
            window._grid.setData(array);
            refreshOutput();
            // 更新输入框显示
            if (window.updateCustomTableInputs) {
                window.updateCustomTableInputs();
            }
            // utils.utils.notify(utils.notifyStatus.SUCCESS, "Content is rendered")
        } else {
            utils.notify(utils.notifyStatus.ERROR, `Oops! Something went wrong while parsing ${type.toUpperCase()}.`)
        }
    }

    function renderSheet(type, sheets) {
        if (sheets.length > 1) {
            utils.DOMCache.get(".choose-sheet").removeClass("hidden");
            let sheetListHTML = sheets.map(s => {
                return `<a href="#" data-sheet-name="${s.name}" role="button">${s.name}</a>`;
            }).join("");
            utils.DOMCache.get(".sheet-default").text(sheets[0].name);
            utils.DOMCache.get(".sheet-list").html(sheetListHTML);
            utils.DOMCache.get(".sheet-list > a").on("click", function (e) {
                e.preventDefault();
                let selected = utils.DOMCache.get(this).data('sheet-name');
                utils.DOMCache.get(".sheet-default").text(selected);
                let sheet = sheets.find(s => s.name === selected)
                importContent.val(sheet.src);
                toggleEmptyState();
                importData(type, sheet.src)
            });
        }
        return sheets.length > 0 ? sheets[0].array : [[]];
    }

    function refreshOutput(event) {
        if (event) {
            let target = event.target;
            let val;
            switch (target.type) {
                case "checkbox":
                    val = target.checked;
                    break;
                case "textarea":
                case "text":
                case "select-one":
                    val = $(target).val();
                    if (val.startsWith("TEMPLATE-")) {
                        let opt = converter.options.magic.magics[val];
                        $("[name='rowsTpl']").val(opt.rowsTpl)
                        $("[name='headerTpl']").val(opt.headerTpl)
                        $("[name='footerTpl']").val(opt.footerTpl)
                        converter.options.magic.rowsTpl = opt.rowsTpl;
                        converter.options.magic.headerTpl = opt.headerTpl;
                        converter.options.magic.footerTpl = opt.footerTpl;
                    }
                    break;
            }
            converter.options[to][$(target).attr('name')] = val;
        }
        utils.asyncCall(function () {
            let array = arrayGlobal.getArray();
            let output = converter[to](array);
            outputContent.val(output);
            updateChooserAndInputs();
        }, 5)
    }


    /****************************** Misc **************************/
    const rapiURL = 'js/lib/rapidoc-min.js';
    utils.DOMCache.get("#api-portal").on('mouseenter', function () {
        let exists = document.querySelector(`script[src='${rapiURL}']`);
        if (!exists) {
            const script = document.createElement('script');
            script.src = rapiURL;
            document.head.appendChild(script);
        }
    });
    // 处理空状态显示/隐藏的通用函数
    function toggleEmptyState() {
        const emptyState = document.getElementById('empty-state');
        if (emptyState) {
            const hasContent = importContent.val().trim().length > 0;
            emptyState.style.opacity = hasContent ? '0' : '';
            emptyState.style.pointerEvents = hasContent ? 'none' : '';
        }
    }

    // 创建防抖的数据导入函数
    const debouncedImportData = utils.PerformanceUtils.debounce(function(from, value) {
        importData(from, value);
        toggleEmptyState();
    }, 300);

    importContent.on('input propertychange', function () {
        // console.log(this.value);
        debouncedImportData(from, importContent.val());
    });
    // 创建防抖的输出刷新函数
    const debouncedRefreshOutput = utils.PerformanceUtils.debounce(function(e) {
        refreshOutput(e);
    }, 200);

    // importContent.on("change", () => importData(from, importContent.val()))

    $(".options .onchange").on('keyup change', (e) => debouncedRefreshOutput(e));
    utils.DOMCache.get("copy").on('click', () => utils.copy(outputContent.get(0)));
    utils.DOMCache.get("download").on('click', () => utils.downloader.download(to, converter.options[to]));
    utils.DOMCache.get("example").on('click', () => {
        let settings = {}
        if (from === 'sql') {
            settings = { create: false }
        } else if (from === 'excel') {
            settings = { textFormat: false }
        } else if (from === 'csv') {
            settings = { doubleQuote: false }
        }

        let example;
        if (from === 'mysql') {
            example = converter["ascii"](arrayGlobal.getExample(), settings);
        } else {
            example = converter[from](arrayGlobal.getExample(), settings);
        }
        importContent.val(example);
        toggleEmptyState();
        importData(from, example)
    });
    $(".editor-filters button[data-click]").on('click', function (e) {
        let name = $(e.currentTarget).data("click");
        console.log(name)
        if (name === "replace") {
            let replace = utils.DOMCache.get('[name="replace"]').val();
            let subst = utils.DOMCache.get('[name="subst"]').val();
            arrayGlobal.replace(replace, subst);
        } else {
            arrayGlobal[name]();
        }

        // 对于重要操作强制同步保存history
        if (['transpose', 'deleteBlank', 'deleteDuplicate', 'uppercase', 'lowercase', 'capitalize'].includes(name)) {
            arrayGlobal.saveHistorySync();
        }

        window._grid.setData(arrayGlobal.getArray());
        refreshOutput();
        // 更新输入框显示
        if (window.updateCustomTableInputs) {
            window.updateCustomTableInputs();
        }
    });


    // 使用事件委托处理Extract按钮点击
    document.addEventListener('click', function(event) {
        // 使用我们的安全 closest 方法
        const extractButton = safeClosest(event.target, '#extract');

        if (extractButton) {
            event.preventDefault();
            event.stopPropagation();

            let url = utils.DOMCache.get("inputUrl").val();
            if (!url || url.trim().length === 0) {
                utils.notify(utils.notifyStatus.ERROR, `URL is required`)
                return;
            }

            // 校验URL格式，必须以http://或https://开头
            url = url.trim();
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                utils.notify(utils.notifyStatus.ERROR, `URL must start with http:// or https://`)
                return;
            }

            const $button = utils.DOMCache.get('extract'); // 使用预定义的key

        // Extract执行函数，支持重试
        function executeExtract(isRetry = false) {
            // 添加加载状态
            $button.addClass('loading').prop('disabled', true);
            $button.find('.icon-extract').addClass('hidden');
            $button.find('.icon-spinner').removeClass('hidden');

            utils.loading();

            // 5秒超时定时器
            const timeoutId = setTimeout(() => {
                if (!isRetry) {
                    console.log('Extract timeout, retrying...');
                    executeExtract(true); // 自动重试一次
                }
            }, 5000);

            $.ajax('GET', corsServer, url, function (data) {
                clearTimeout(timeoutId);
                importContent.val(data);
                toggleEmptyState();
                importData(from, data);
                utils.loaded();

                // 移除加载状态
                $button.removeClass('loading').prop('disabled', false);
                $button.find('.icon-extract').removeClass('hidden');
                $button.find('.icon-spinner').addClass('hidden');

                // 关闭下拉面板
                if (window.closeExtractPanel) {
                    window.closeExtractPanel();
                }
            }, function (statusCode, statusText, errorThrown) {
                clearTimeout(timeoutId);
                utils.notify(utils.notifyStatus.ERROR, `${statusCode}: ${statusCode} ${statusText}`)
                utils.loaded();

                // 移除加载状态
                $button.removeClass('loading').prop('disabled', false);
                $button.find('.icon-extract').removeClass('hidden');
                $button.find('.icon-spinner').addClass('hidden');
            });
        }

            executeExtract();
        }
    });

    // Auto close brackets in magic generator
    const closeChars = new Map([['{', '}'], ['[', ']'], ['(', ')']]);
    const autoCloseBrackets = function (e) {
        const pos = e.target.selectionStart;
        const val = [...e.target.value];
        const char = val.slice(pos - 1, pos)[0];
        const closeChar = closeChars.get(char);
        if (closeChar && e.inputType !== "deleteContentBackward") {
            val.splice(pos, 0, closeChar);
            e.target.value = val.join('');
            e.target.selectionEnd = pos;
        }
    }
    document.querySelectorAll('.options.magic textarea._textarea').forEach(el => {
        el.addEventListener('input', autoCloseBrackets);
    });


    utils.DOMCache.get(".editor-switch").on('click', function () {
        utils.DOMCache.get(this).toggleClass("icon-add").toggleClass("icon-minus");
        utils.DOMCache.get(".table-chooser-wrapper").toggleClass("hidden");
        utils.DOMCache.get(".editor-block").toggleClass("hidden");
        utils.DOMCache.get(".editor-container").toggleClass("opacity-50").toggleClass("shadow");
        reloadDataGrid();
    });

    // DataGridXL 滚动数据时会改变背景色
    new MutationObserver(() => {
        document.body.style.color = ""
        document.body.style.backgroundColor = ""
    }).observe(document.body, {attributes: true, attributeFilter: ['style']});

    // 自定义行和列 - 兼容URL参数
    const rows = +urlParams.get('rows');
    const cols = +urlParams.get('cols');
    if (rows > 0 && cols > 0) {
        // 如果URL中有参数，先设置输入框的值
        utils.DOMCache.get("customRows").val(rows);
        utils.DOMCache.get("customCols").val(cols);
        applyChoose(rows, cols);
    }

    /****************************** UI Components ******************************/

    // FAQ Component
    const faqManager = {
        init() {
            // 使用原生事件绑定，监听FAQ按钮点击
            document.addEventListener('click', function(e) {
                // 检查是否点击了FAQ按钮
                const faqButton = safeClosest(e.target, '.faq-button');
                if (faqButton) {
                    e.preventDefault();
                    faqManager.toggle(faqButton);
                }
            });
        },

        toggle(button) {
            // 确保button是DOM元素
            if (!button || !button.nodeType) {
                return;
            }

            const content = button.nextElementSibling;
            const icon = button.querySelector('svg');

            if (!content || !icon) {
                return;
            }

            // 检查当前的display样式来判断显示状态
            const currentDisplay = content.style.display;
            const isHidden = currentDisplay === 'none';

            if (isHidden) {
                content.style.display = 'block';
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.style.display = 'none';
                icon.style.transform = 'rotate(0deg)';
            }
        }
    };


    // Panel Resizer - 生成器面板大小调整
    const panelResizer = {
        init() {
            const resizer = document.getElementById('resizer');
            const optionsPanel = document.getElementById('options-panel');
            const outputPanel = document.getElementById('output-panel');
            const container = document.getElementById('resizable-container');

            if (!resizer || !optionsPanel || !outputPanel || !container) {
                return;
            }

            let isResizing = false;

            // 拖拽事件 - 使用原生事件
            resizer.addEventListener('mousedown', (e) => {
                isResizing = true;
                document.body.classList.add('resizing');
                e.preventDefault();
                document.addEventListener('selectstart', this.preventSelect);
            });

            document.addEventListener('mousemove', (e) => {
                if (!isResizing) return;
                e.preventDefault();

                const containerRect = container.getBoundingClientRect();
                const containerWidth = containerRect.width;
                const mouseX = e.clientX - containerRect.left;

                let leftPercentage = (mouseX / containerWidth) * 100;
                leftPercentage = Math.max(15, Math.min(75, leftPercentage));

                optionsPanel.style.width = `${leftPercentage}%`;
                localStorage.setItem('tableconvert-panel-width', leftPercentage.toString());
            });

            document.addEventListener('mouseup', () => {
                if (isResizing) {
                    isResizing = false;
                    document.body.classList.remove('resizing');
                    document.removeEventListener('selectstart', this.preventSelect);
                }
            });

            // 双击重置
            resizer.addEventListener('dblclick', () => {
                optionsPanel.style.width = '25%';
                localStorage.removeItem('tableconvert-panel-width');
            });

            // 窗口大小变化处理
            window.addEventListener('resize', () => {
                if (window.innerWidth < 768) {
                    optionsPanel.style.width = '';
                } else {
                    this.restorePanelWidth();
                }
            });

            // 初始化
            this.restorePanelWidth();
        },

        preventSelect(e) {
            e.preventDefault();
            return false;
        },

        restorePanelWidth() {
            const savedWidth = localStorage.getItem('tableconvert-panel-width');
            const optionsPanel = document.getElementById('options-panel');
            if (savedWidth && window.innerWidth >= 768 && optionsPanel) {
                const width = parseFloat(savedWidth);
                if (width >= 15 && width <= 75) {
                    optionsPanel.style.width = `${width}%`;
                }
            }
        }
    };

    // 初始化所有组件
    faqManager.init();
    panelResizer.init();

});

// Extract面板处理
(function() {
    'use strict';

    let isExtractPanelOpen = false;

    function init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', bindEvents);
        } else {
            bindEvents();
        }
    }

    function bindEvents() {
        const extractButton = document.querySelector('#extract-panel [data-toggle="dropdown"]');
        if (!extractButton) return;

        // Extract按钮点击
        extractButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleExtractPanel();
        });

        // 阻止面板内部点击冒泡，但允许Extract按钮的点击事件传播
        document.addEventListener('click', function(e) {
            const dropdownPanel = document.querySelector('#extract-panel [data-close]');
            if (dropdownPanel && dropdownPanel.contains(e.target)) {
                // 如果点击的是Extract按钮或其子元素，不阻止事件传播
                const extractButton = safeClosest(e.target, '#extract');
                if (!extractButton) {
                    e.stopPropagation();
                }
            }
        }, true);

        // 点击外部关闭
        document.addEventListener('click', function(e) {
            const extractPanel = document.getElementById('extract-panel');
            if (isExtractPanelOpen && extractPanel && !extractPanel.contains(e.target)) {
                closeExtractPanel();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', function(e) {
            if (e.keyCode === 27 && isExtractPanelOpen) {
                closeExtractPanel();
            }
        });
    }

    function toggleExtractPanel() {
        if (isExtractPanelOpen) {
            closeExtractPanel();
        } else {
            openExtractPanel();
        }
    }

    function openExtractPanel() {
        const dropdown = document.querySelector('#extract-panel [data-close]');
        if (!dropdown) return;

        // 直接显示面板，使用相对定位
        dropdown.classList.remove('invisible');
        dropdown.classList.add('show');
        isExtractPanelOpen = true;

        // 自动聚焦
        const input = dropdown.querySelector('#input-url');
        if (input) {
            setTimeout(() => input.focus(), 100);
        }
    }

    function closeExtractPanel() {
        const dropdown = document.querySelector('#extract-panel [data-close]');
        if (!dropdown) return;

        dropdown.classList.add('invisible');
        dropdown.classList.remove('show');
        isExtractPanelOpen = false;
    }

    // 供其他地方使用
    window.closeExtractPanel = closeExtractPanel;

    // 移动端提取按钮处理
    utils.DOMCache.get("extractMobile").on("click", function(e) {
        e.preventDefault();
        // 创建一个简单的提示输入框
        const url = prompt("请输入要提取数据的URL:");
        if (url && url.trim()) {
            // 设置URL到隐藏的输入框
            utils.DOMCache.get("inputUrl").val(url.trim());
            // 触发提取按钮点击
            utils.DOMCache.get("extract").click();
        }
    });

    init();
})();
