---
title: API ಉಲ್ಲೇಖ
date: 2025-07-28 12:38:19
url: "/kn/api/"
type: dashboard
layout: api
---

TableConvert API ವಿವಿಧ ಫಾರ್ಮ್ಯಾಟ್‌ಗಳ ನಡುವೆ ಡೇಟಾ ಪರಿವರ್ತನೆಯ ಪ್ರಕ್ರಿಯೆಯನ್ನು ಸುಗಮಗೊಳಿಸಲು ವಿನ್ಯಾಸಗೊಳಿಸಲಾದ ಬಹುಮುಖ ಸಾಧನವಾಗಿದೆ. **{{< count >}}** ವಿಭಿನ್ನ ಪರಿವರ್ತಕಗಳಿಗೆ ಪ್ರವೇಶದೊಂದಿಗೆ, ಈ API CSV, Excel, HTML, JSON, Markdown ಮತ್ತು ಇನ್ನಷ್ಟು ಸೇರಿದಂತೆ ಅನೇಕ ಫೈಲ್ ಪ್ರಕಾರಗಳು ಮತ್ತು ರಚನೆಗಳಾದ್ಯಂತ ನಿರಂತರ ಡೇಟಾ ಪರಿವರ್ತನೆಯನ್ನು ಸುಗಮಗೊಳಿಸುತ್ತದೆ.

TableConvert API ಅನ್ನು ಬಳಸಲು ಪ್ರಾರಂಭಿಸಲು, ಈ ಹಂತಗಳನ್ನು ಅನುಸರಿಸಿ:

#### ಹಂತ 1: API ಕೀಗಾಗಿ ಸೈನ್ ಅಪ್ ಮಾಡಿ

1. **ಖಾತೆ ರಚಿಸಿ**: ನಿಮ್ಮ ಅನನ್ಯ `API ಕೀ` ಪಡೆಯಲು ಖಾತೆಗಾಗಿ ಸೈನ್ ಅಪ್ ಮಾಡಿ.
2. **[ಬೆಲೆ](/pricing/) ಪುಟಕ್ಕೆ ಭೇಟಿ ನೀಡಿ**: ನಿಮ್ಮ ಯೋಜನೆಯನ್ನು ಆಯ್ಕೆ ಮಾಡಲು ಬೆಲೆ ಪುಟಕ್ಕೆ ನ್ಯಾವಿಗೇಟ್ ಮಾಡಿ.

#### ಹಂತ 2: API ಕೀ ನಿರ್ವಹಿಸಿ

1. **[ದೃಢೀಕರಣ](#auth) ಪುಟವನ್ನು ಪ್ರವೇಶಿಸಿ**: ಈ ಪುಟವು ನಿಮ್ಮ API ಕೀಯನ್ನು ನಿರ್ವಹಿಸಲು ಅನುಮತಿಸುತ್ತದೆ.
2. **ನಿಮ್ಮ API ಕೀ ಹೊಂದಿಸಿ**: ನಿಮ್ಮ API ಕೀ ಇದ್ದಾಗ, ದಾಖಲಾತಿಯಿಂದ API ಕರೆ ಪರೀಕ್ಷೆಯನ್ನು ಸಕ್ರಿಯಗೊಳಿಸಲು ನಿಗದಿತ ವಿಭಾಗದಲ್ಲಿ ಅದನ್ನು ನಮೂದಿಸಿ.

ದೃಢೀಕರಣ ಅಥವಾ ಯಾವುದೇ ಇತರ ವಿಚಾರಣೆಗಳಿಗೆ ಸಹಾಯಕ್ಕಾಗಿ, ದಾಖಲಾತಿಯನ್ನು ಉಲ್ಲೇಖಿಸಿ ಅಥವಾ ನಮ್ಮ ಬೆಂಬಲ ತಂಡವನ್ನು [ಸಂಪರ್ಕಿಸಿ](https://goo.gl/forms/xPp7Vw50u6MmOoIF2).

#### ಹಂತ 3: API ಪರೀಕ್ಷೆ

1. **ದಾಖಲಾತಿಯಲ್ಲಿ ಪ್ರಯತ್ನಿಸಿ ಬಟನ್ ಬಳಸಿ**: ನೀವು ಪರೀಕ್ಷಿಸಲು ಬಯಸುವ API ಎಂಡ್‌ಪಾಯಿಂಟ್‌ಗೆ ನ್ಯಾವಿಗೇಟ್ ಮಾಡಿ ಮತ್ತು `ಪ್ರಯತ್ನಿಸಿ` ಬಟನ್ ಕ್ಲಿಕ್ ಮಾಡಿ.
2. **ಕೋಡ್ ಉದಾಹರಣೆಗಳನ್ನು ಚಲಾಯಿಸಿ**: API ಎಂಡ್‌ಪಾಯಿಂಟ್‌ಗಳನ್ನು ಪರೀಕ್ಷಿಸಲು ವಿವಿಧ ಪ್ರೋಗ್ರಾಮಿಂಗ್ ಭಾಷೆಗಳಲ್ಲಿ ಒದಗಿಸಲಾದ ಕೋಡ್ ಉದಾಹರಣೆಗಳನ್ನು ಬಳಸಿ.

#### ವಿನಂತಿಗಳನ್ನು ಮಾಡುವುದು

API ವಿನಂತಿಗಳನ್ನು ಮಾಡುವಾಗ, ಈ ಕೆಳಗಿನವುಗಳನ್ನು ಮನಸ್ಸಿನಲ್ಲಿಟ್ಟುಕೊಳ್ಳಿ:

1. **ವಿಷಯ ಪ್ರಕಾರ**: ಎಲ್ಲಾ API ಎಂಡ್‌ಪಾಯಿಂಟ್‌ಗಳು `multipart/form-data` ವಿಷಯ ಪ್ರಕಾರವನ್ನು ಬಳಸುತ್ತವೆ.
2. **ಅಧಿಕಾರ ಹೆಡರ್**: ನಿಮ್ಮ API ಕರೆಗಳಲ್ಲಿ ಅಧಿಕಾರ ಹೆಡರ್ ಅನ್ನು ಸೇರಿಸಿ.

ಉದಾಹರಣೆಗೆ, `curl` ಬಳಸಿ, ನೀವು ಈ ಕೆಳಗಿನಂತೆ ಅಧಿಕಾರ ಹೆಡರ್ ಅನ್ನು ಸೇರಿಸಬಹುದು:

```bash
curl -X POST "https://api.tableconvert.com/csv-to-markdown" \
  -H "Authorization: Bearer ${API_Key}" \
  -F "data=name,age"
```
