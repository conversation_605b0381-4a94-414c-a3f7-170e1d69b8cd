---
title: API 참조
date: 2025-07-28 12:38:19
url: "/ko/api/"
type: dashboard
layout: api
---

TableConvert API는 다양한 형식 간의 데이터 변환 프로세스를 간소화하도록 설계된 다목적 도구입니다. **{{< count >}}**개의 서로 다른 변환기에 대한 액세스를 통해 이 API는 CSV, Excel, HTML, JSON, Markdown 등을 포함한 다양한 파일 유형과 구조에서 원활한 데이터 변환을 촉진합니다.

TableConvert API 사용을 시작하려면 다음 단계를 따르세요:

#### 1단계: API 키 등록

1. **계정 생성**: 고유한 `API 키`를 얻기 위해 계정에 가입하세요.
2. **[가격](/pricing/) 페이지 방문**: 가격 페이지로 이동하여 플랜을 선택하세요.

#### 2단계: API 키 관리

1. **[인증](#auth) 페이지 접근**: 이 페이지에서 API 키를 관리할 수 있습니다.
2. **API 키 설정**: API 키를 받으면 문서에서 API 호출 테스트를 활성화하기 위해 지정된 섹션에 입력하세요.

인증이나 기타 문의사항에 대한 도움이 필요하면 문서를 참조하거나 지원팀에 [문의](https://goo.gl/forms/xPp7Vw50u6MmOoIF2)하세요.

#### 3단계: API 테스트

1. **문서의 시도 버튼 사용**: 테스트하려는 API 엔드포인트로 이동하여 `시도` 버튼을 클릭하세요.
2. **코드 예제 실행**: 다양한 프로그래밍 언어로 제공된 코드 예제를 활용하여 API 엔드포인트를 테스트하세요.

#### 요청하기

API 요청을 할 때 다음 사항을 염두에 두세요:

1. **콘텐츠 유형**: 모든 API 엔드포인트는 `multipart/form-data` 콘텐츠 유형을 사용합니다.
2. **인증 헤더**: API 호출에 인증 헤더를 포함하세요.

예를 들어, `curl`을 사용하여 다음과 같이 인증 헤더를 추가할 수 있습니다:

```bash
curl -X POST "https://api.tableconvert.com/csv-to-markdown" \
  -H "Authorization: Bearer ${API_Key}" \
  -F "data=name,age"
```
