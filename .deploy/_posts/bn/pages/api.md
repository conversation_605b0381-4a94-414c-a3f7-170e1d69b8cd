---
title: API রেফারেন্স
date: 2025-07-28 12:38:19
url: "/bn/api/"
type: dashboard
layout: api
---

TableConvert API হল একটি বহুমুখী টুল যা বিভিন্ন ফরম্যাটের মধ্যে ডেটা রূপান্তরের প্রক্রিয়াকে সহজ করার জন্য ডিজাইন করা হয়েছে। **{{< count >}}** টি বিভিন্ন কনভার্টারের অ্যাক্সেস সহ, এই API CSV, Excel, HTML, JSON, Markdown এবং আরও অনেক কিছু সহ বিভিন্ন ফাইল টাইপ এবং কাঠামোর মধ্যে নিরবচ্ছিন্ন ডেটা রূপান্তর সহজ করে।

TableConvert API ব্যবহার শুরু করতে, এই পদক্ষেপগুলি অনুসরণ করুন:

#### ধাপ ১: API কী এর জন্য সাইন আপ করুন

1. **একটি অ্যাকাউন্ট তৈরি করুন**: আপনার অনন্য `API কী` পেতে একটি অ্যাকাউন্টের জন্য সাইন আপ করুন।
2. **[মূল্য নির্ধারণ](/pricing/) পৃষ্ঠা দেখুন**: আপনার পরিকল্পনা বেছে নিতে মূল্য নির্ধারণ পৃষ্ঠায় নেভিগেট করুন।

#### ধাপ ২: API কী পরিচালনা করুন

1. **[প্রমাণীকরণ](#auth) পৃষ্ঠায় অ্যাক্সেস করুন**: এই পৃষ্ঠাটি আপনাকে আপনার API কী পরিচালনা করতে দেয়।
2. **আপনার API কী সেট করুন**: একবার আপনার API কী থাকলে, ডকুমেন্টেশন থেকে API কল পরীক্ষা সক্ষম করতে নির্ধারিত বিভাগে এটি প্রবেশ করান।

প্রমাণীকরণ বা অন্য কোনো অনুসন্ধানের জন্য সহায়তার জন্য, ডকুমেন্টেশন দেখুন বা আমাদের সাপোর্ট টিমের সাথে [যোগাযোগ](https://goo.gl/forms/xPp7Vw50u6MmOoIF2) করুন।

#### ধাপ ৩: API পরীক্ষা করা

1. **ডকুমেন্টেশনে ট্রাই বোতাম ব্যবহার করুন**: আপনি যে API এন্ডপয়েন্ট পরীক্ষা করতে চান সেখানে নেভিগেট করুন এবং `ট্রাই` বোতামে ক্লিক করুন।
2. **কোড উদাহরণ চালান**: API এন্ডপয়েন্ট পরীক্ষা করতে বিভিন্ন প্রোগ্রামিং ভাষায় প্রদত্ত কোড উদাহরণ ব্যবহার করুন।

#### অনুরোধ করা

API অনুরোধ করার সময়, নিম্নলিখিত বিষয়গুলি মনে রাখুন:

1. **কন্টেন্ট টাইপ**: সমস্ত API এন্ডপয়েন্ট `multipart/form-data` কন্টেন্ট টাইপ ব্যবহার করে।
2. **অনুমোদন হেডার**: আপনার API কলে অনুমোদন হেডার অন্তর্ভুক্ত করুন।

উদাহরণস্বরূপ, `curl` ব্যবহার করে, আপনি নিম্নরূপ অনুমোদন হেডার যোগ করতে পারেন:

```bash
curl -X POST "https://api.tableconvert.com/csv-to-markdown" \
  -H "Authorization: Bearer ${API_Key}" \
  -F "data=name,age"
```
