---
title: مرجع API
date: 2025-07-28 12:38:19
url: "/ar/api/"
type: dashboard
layout: api
---

واجهة برمجة التطبيقات TableConvert هي أداة متعددة الاستخدامات مصممة لتبسيط عملية تحويل البيانات بين التنسيقات المختلفة. مع الوصول إلى **{{< count >}}** محول مختلف، تسهل هذه الواجهة التحويل السلس للبيانات عبر مجموعة متنوعة من أنواع الملفات والهياكل، بما في ذلك على سبيل المثال لا الحصر CSV وExcel وHTML وJSON وMarkdown والمزيد.

لبدء استخدام واجهة برمجة التطبيقات TableConvert، اتبع هذه الخطوات:

#### الخطوة 1: التسجيل للحصول على مفتاح API

1. **إنشاء حساب**: سجل للحصول على حساب للحصول على `مفتاح API` الفريد الخاص بك.
2. **زيارة صفحة [التسعير](/pricing/)**: انتقل إلى صفحة التسعير لاختيار خطتك.

#### الخطوة 2: إدارة مفتاح API

1. **الوصول إلى صفحة [المصادقة](#auth)**: تتيح لك هذه الصفحة إدارة مفتاح API الخاص بك.
2. **تعيين مفتاح API الخاص بك**: بمجرد حصولك على مفتاح API، أدخله في القسم المخصص لتمكين اختبار استدعاءات API من الوثائق.

للحصول على المساعدة في المصادقة أو أي استفسارات أخرى، راجع الوثائق أو [اتصل](https://goo.gl/forms/xPp7Vw50u6MmOoIF2) بفريق الدعم لدينا.

#### الخطوة 3: اختبار API

1. **استخدام زر التجربة في الوثائق**: انتقل إلى نقطة نهاية API التي تريد اختبارها وانقر على زر `تجربة`.
2. **تشغيل أمثلة الكود**: استخدم أمثلة الكود المقدمة بلغات البرمجة المختلفة لاختبار نقاط نهاية API.

#### إجراء الطلبات

عند إجراء طلبات API، ضع في اعتبارك ما يلي:

1. **نوع المحتوى**: تستخدم جميع نقاط نهاية API نوع المحتوى `multipart/form-data`.
2. **رأس التفويض**: قم بتضمين رأس التفويض في استدعاءات API الخاصة بك.

على سبيل المثال، باستخدام `curl`، يمكنك إضافة رأس التفويض كما يلي:

```bash
curl -X POST "https://api.tableconvert.com/csv-to-markdown" \
  -H "Authorization: Bearer ${API_Key}" \
  -F "data=name,age"
```
